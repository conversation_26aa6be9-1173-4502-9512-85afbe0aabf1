/* Radhe World Component Styles - Matching Tanishq World Design */
.Radhe-world {
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 20px;
  font-family: 'Georgia', serif;
  background-color: #fafafa;
}

/* Header Styles */
.Radhe-header {
  text-align: center;
  margin-bottom: 50px;
}

.Radhe-title {
  font-size: 2.8rem;
  font-weight: 400;
  color: #8B4513;
  margin: 0 0 15px 0;
  letter-spacing: 1px;
  font-family: 'Georgia', serif;
}

.Radhe-subtitle {
  font-size: 1.2rem;
  color: #888;
  margin: 0;
  font-style: italic;
  font-weight: 300;
  letter-spacing: 0.5px;
}

/* Grid Layout - 2x2 Grid */
.Radhe-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
  height: 650px;
  max-width: 900px;
  margin: 0 auto;
}

/* Individual Item Styles */
.Radhe-item {
  position: relative;
  overflow: hidden;
  border-radius: 15px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.Radhe-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.Radhe-link {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  color: inherit;
}

.Radhe-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.Radhe-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.Radhe-item:hover .Radhe-image {
  transform: scale(1.08);
}

/* Overlay Styles */
.Radhe-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.75));
  padding: 40px 25px 25px;
  display: flex;
  align-items: flex-end;
}

.Radhe-item-title {
  color: white;
  font-size: 2rem;
  font-weight: 500;
  margin: 0;
  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.7);
  letter-spacing: 1px;
  font-family: 'Georgia', serif;
}

/* Specific Grid Positioning */
.wedding-item {
  grid-column: 1;
  grid-row: 1;
}

.diamond-item {
  grid-column: 2;
  grid-row: 1;
}

.gold-item {
  grid-column: 1;
  grid-row: 2;
}

.dailywear-item {
  grid-column: 2;
  grid-row: 2;
}

/* Responsive Design */
@media (max-width: 768px) {
  .Radhe-world {
    padding: 40px 15px;
  }

  .Radhe-title {
    font-size: 2.2rem;
  }

  .Radhe-subtitle {
    font-size: 1rem;
  }

  .Radhe-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 280px);
    height: auto;
    gap: 15px;
    max-width: 100%;
  }

  .Radhe-item-title {
    font-size: 1.6rem;
  }

  .Radhe-overlay {
    padding: 25px 20px 20px;
  }

  /* Reset grid positioning for mobile */
  .wedding-item,
  .diamond-item,
  .gold-item,
  .dailywear-item {
    grid-column: 1;
    grid-row: auto;
  }
}

@media (max-width: 480px) {
  .Radhe-world {
    padding: 30px 10px;
  }

  .Radhe-title {
    font-size: 1.9rem;
  }

  .Radhe-subtitle {
    font-size: 0.9rem;
  }

  .Radhe-grid {
    grid-template-rows: repeat(4, 250px);
    gap: 12px;
  }

  .Radhe-item-title {
    font-size: 1.4rem;
  }

  .Radhe-overlay {
    padding: 20px 15px 15px;
  }
}

/* Large screens optimization */
@media (min-width: 1200px) {
  .Radhe-world {
    padding: 80px 20px;
  }

  .Radhe-title {
    font-size: 3.2rem;
  }

  .Radhe-subtitle {
    font-size: 1.3rem;
  }

  .Radhe-grid {
    height: 700px;
    max-width: 1000px;
  }

  .Radhe-item-title {
    font-size: 2.2rem;
  }
}
