/* Trending Component Styles */

.trending-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 80px 40px;
  background-color: #ffffff;
}

/* Header Styles */
.trending-header {
  text-align: center;
  margin-bottom: 60px;
}

.trending-title {
  font-size: 2.8rem;
  font-weight: 400;
  color: #2c2c2c;
  margin-bottom: 12px;
  font-family: 'Georgia', serif;
  line-height: 1.2;
}

.trending-subtitle {
  font-size: 1.1rem;
  color: #8a8a8a;
  margin-bottom: 0;
  font-weight: 400;
  letter-spacing: 0.3px;
}

/* Grid Layout */
.trending-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  max-width: 1000px;
  margin: 0 auto;
}

/* Trending Item Styles */
.trending-item {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background: #ffffff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.trending-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.trending-link {
  display: block;
  text-decoration: none;
  color: inherit;
  width: 100%;
  height: 100%;
}

.trending-card {
  position: relative;
  width: 100%;
  height: 100%;
}

.trending-image-container {
  position: relative;
  width: 100%;
  height: 350px;
  overflow: hidden;
  border-radius: 12px;
}

.trending-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.trending-item:hover .trending-image {
  transform: scale(1.05);
}

/* Overlay Styles */
.trending-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    transparent 100%
  );
  padding: 30px 20px 20px;
  transition: opacity 0.3s ease;
}

.trending-overlay-content {
  text-align: center;
}

.trending-overlay-title {
  color: #ffffff;
  font-size: 1.4rem;
  font-weight: 500;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .trending-section {
    padding: 60px 30px;
  }
  
  .trending-grid {
    gap: 25px;
  }
  
  .trending-image-container {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .trending-section {
    padding: 50px 20px;
  }
  
  .trending-title {
    font-size: 2.2rem;
  }
  
  .trending-subtitle {
    font-size: 1rem;
  }
  
  .trending-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    max-width: 400px;
  }
  
  .trending-image-container {
    height: 280px;
  }
  
  .trending-overlay-title {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .trending-section {
    padding: 40px 15px;
  }
  
  .trending-title {
    font-size: 1.8rem;
  }
  
  .trending-subtitle {
    font-size: 0.9rem;
  }
  
  .trending-image-container {
    height: 250px;
  }
  
  .trending-overlay {
    padding: 20px 15px 15px;
  }
  
  .trending-overlay-title {
    font-size: 1.1rem;
  }
}

/* Specific styling for different trending categories */
.trending-auspicious .trending-overlay {
  background: linear-gradient(
    to top,
    rgba(139, 69, 19, 0.8) 0%,
    rgba(139, 69, 19, 0.4) 50%,
    transparent 100%
  );
}

.trending-gifting .trending-overlay {
  background: linear-gradient(
    to top,
    rgba(220, 20, 60, 0.8) 0%,
    rgba(220, 20, 60, 0.4) 50%,
    transparent 100%
  );
}

.trending-18kt .trending-overlay {
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    transparent 100%
  );
}
