'use client';

import React from 'react';
import '../style/trending.css';

interface TrendingItem {
  id: number;
  src: string;
  alt: string;
  href: string;
  title: string;
  className: string;
}

const Trending: React.FC = () => {
  const trendingItems: TrendingItem[] = [
    {
      id: 1,
      src: "/images/image1.png",
      alt: "Auspicious Occasion Jewelry",
      href: "#auspicious-occasion",
      title: "Auspicious Occasion",
      className: "trending-auspicious"
    },
    {
      id: 2,
      src: "/images/image2.png",
      alt: "Gifting Jewellery",
      href: "#gifting-jewellery",
      title: "Gifting Jewellery",
      className: "trending-gifting"
    },
    {
      id: 3,
      src: "/images/image3.png",
      alt: "18Kt Jewellery",
      href: "#18kt-jewellery",
      title: "18Kt Jewellery",
      className: "trending-18kt"
    }
  ];

  const handleTrendingClick = (href: string) => {
    console.log(`Navigating to: ${href}`);
    // Add your navigation logic here
  };

  return (
    <div className="trending-section">
      <div className="trending-header">
        <h1 className="trending-title">Trending Now</h1>
        <p className="trending-subtitle">Jewellery pieces everyone's eyeing right now</p>
      </div>

      <div className="trending-grid">
        {trendingItems.map((item) => (
          <div key={item.id} className={`trending-item ${item.className}`}>
            <a
              href={item.href}
              onClick={(e) => {
                e.preventDefault();
                handleTrendingClick(item.href);
              }}
              className="trending-link"
            >
              <div className="trending-card">
                <div className="trending-image-container">
                  <img
                    src={item.src}
                    alt={item.alt}
                    className="trending-image"
                  />
                  <div className="trending-overlay">
                    <div className="trending-overlay-content">
                      <h3 className="trending-overlay-title">{item.title}</h3>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Trending;
