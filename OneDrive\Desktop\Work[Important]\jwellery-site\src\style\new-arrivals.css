.new-arrivals {
  background: linear-gradient(135deg, #d4b896 0%, #c9a876 50%, #b8956a 100%);
  position: relative;
  padding: 80px 0;
  overflow: hidden;
}

.new-arrivals::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 40%;
  height: 100%;
  background: url('/main-images/image-3.png') no-repeat right center;
  background-size: contain;
  opacity: 0.2;
  z-index: 1;
}

.new-arrivals-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

.new-arrivals-content {
  display: flex;
  align-items: center;
  gap: 60px;
}

.new-arrivals-text {
  flex: 1;
  color: white;
}

.new-arrivals-title {
  font-size: 3rem;
  font-weight: 300;
  margin: 0 0 20px 0;
  letter-spacing: 2px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.new-arrivals-subtitle {
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
  opacity: 0.95;
  font-weight: 300;
}

.new-arrivals-products {
  display: flex;
  gap: 20px;
  flex: 1;
}

.product-card {
  flex: 1;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
}

.product-image {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.product-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-img {
  transform: scale(1.05);
}

.product-label {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 30px 20px 20px;
  color: white;
}

.product-label span {
  font-size: 1.2rem;
  font-weight: 500;
  letter-spacing: 1px;
  text-transform: uppercase;
}

/* Responsive Design */
@media (max-width: 768px) {
  .new-arrivals {
    padding: 60px 0;
  }
  
  .new-arrivals-content {
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }
  
  .new-arrivals-title {
    font-size: 2.5rem;
  }
  
  .new-arrivals-subtitle {
    font-size: 1rem;
  }
  
  .new-arrivals-products {
    flex-direction: column;
    gap: 20px;
  }
  
  .product-image {
    height: 200px;
  }
}

@media (max-width: 480px) {
  .new-arrivals {
    padding: 40px 0;
  }
  
  .new-arrivals-container {
    padding: 0 15px;
  }
  
  .new-arrivals-title {
    font-size: 2rem;
  }
  
  .new-arrivals-subtitle {
    font-size: 0.9rem;
  }
  
  .product-image {
    height: 180px;
  }
  
  .product-label span {
    font-size: 1rem;
  }
}
