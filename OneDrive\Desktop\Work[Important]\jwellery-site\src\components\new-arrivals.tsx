import React from 'react';
import Image from 'next/image';
import '../style/new-arrivals.css';

const NewArrivals: React.FC = () => {
  return (
    <section className="new-arrivals">
      <div className="new-arrivals-container">
        <div className="new-arrivals-content">
          <div className="new-arrivals-text">
            <h2 className="new-arrivals-title">New Arrivals</h2>
            <p className="new-arrivals-subtitle">
              New Arrivals Dropping Daily. Monday through Friday.<br />
              Explore the latest collection here.
            </p>
          </div>
          
          <div className="new-arrivals-products">
            <div className="product-card">
              <div className="product-image">
                <Image
                  src="/main-images/image-1.png"
                  alt="Mangalsutra Collection"
                  className="product-img"
                  width={300}
                  height={250}
                />
                <div className="product-label">
                  <span>Mangalsutra</span>
                </div>
              </div>
            </div>

            <div className="product-card">
              <div className="product-image">
                <Image
                  src="/main-images/image-2.png"
                  alt="Pendants Collection"
                  className="product-img"
                  width={300}
                  height={250}
                />
                <div className="product-label">
                  <span>Pendants</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tanishq Assurance Section */}
      <div className="tanishq-assurance">
        <div className="assurance-container">
          <div className="assurance-content">
            <div className="assurance-text">
              <h3 className="assurance-title">
                Tanishq <span className="assurance-highlight">Assurance</span>
              </h3>
              <p className="assurance-subtitle">
                Gold has always cherished by you
              </p>
            </div>

            <div className="assurance-features">
              <div className="feature-item">
                <div className="feature-icon">
                  <Image
                    src="https://cdn-icons-png.flaticon.com/512/2910/2910791.png"
                    alt="Quality Craftsmanship"
                    className="feature-img"
                    width={30}
                    height={30}
                  />
                </div>
                <p className="feature-text">
                  Quality<br />
                  Craftsmanship
                </p>
              </div>

              <div className="feature-item">
                <div className="feature-icon">
                  <Image
                    src="https://cdn-icons-png.flaticon.com/512/1041/1041883.png"
                    alt="Ethically Sourced"
                    className="feature-img"
                    width={30}
                    height={30}
                  />
                </div>
                <p className="feature-text">
                  Ethically<br />
                  Sourced
                </p>
              </div>

              <div className="feature-item">
                <div className="feature-icon">
                  <Image
                    src="https://cdn-icons-png.flaticon.com/512/2910/2910769.png"
                    alt="100% Transparency"
                    className="feature-img"
                    width={30}
                    height={30}
                  />
                </div>
                <p className="feature-text">
                  100%<br />
                  Transparency
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default NewArrivals;
