/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/style/banner.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/* Banner Slideshow Styles */

.slideshow-container {
    position: relative;
    width: 100vw;
    margin: 0;
    overflow: hidden;
}

.slides-wrapper {
    display: flex;
    width: 600%; /* 6 slides × 100% = 600% (4 original + 2 duplicates) */
    height: 600px;
}

.slide {
    width: 16.666667%; /* Each slide takes 16.666667% of the wrapper (100% / 6 slides) */
    height: 100%;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
}

.nav-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 24px;
    color: #333;
    transition: all 0.3s ease;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.nav-button:hover {
    background: white;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.nav-button:active {
    transform: translateY(-50%) scale(0.95);
}

.prev {
    left: 20px;
}

.next {
    right: 20px;
}

.dots-container {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
}

.dot {
    height: 15px;
    width: 15px;
    margin: 0 5px;
    background-color: #bbb;
    border-radius: 50%;
    display: inline-block;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot:hover {
    background-color: #717171;
    transform: scale(1.2);
}

.dot.active {
    background-color: #667eea;
    transform: scale(1.3);
}



@media (max-width: 768px) {
    .slideshow-container {
        width: 100%;
    }

    .slides-wrapper {
        height: 450px; /* Increased height to accommodate full image */
    }

    .slide img {
        object-fit: contain; /* Show full image without cropping */
        object-position: center;
        background-color: #ffffff; /* White background for any letterboxing */
        width: 100%;
        height: 100%;
    }

    /* Hide navigation buttons on mobile */
    .nav-button {
        display: none;
    }

    /* Hide dots container on mobile */
    .dots-container {
        display: none;
    }
}

/* Additional breakpoint for very small screens */
@media (max-width: 480px) {
    .slides-wrapper {
        height: 400px; /* Increased height for small screens */
    }

    .slide img {
        object-fit: contain; /* Ensure full image is visible */
        object-position: center;
    }
}
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/style/categories.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/* Categories Component Styles */

.categories-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 80px 40px;
  background-color: #ffffff;
}

/* Header Styles */
.categories-header {
  text-align: center;
  margin-bottom: 60px;
}

.categories-title {
  font-size: 2.8rem;
  font-weight: 400;
  color: #2c2c2c;
  margin-bottom: 12px;
  font-family: 'Georgia', serif;
  line-height: 1.2;
}

.categories-subtitle {
  font-size: 1.1rem;
  color: #8a8a8a;
  margin-bottom: 0;
  font-weight: 400;
  letter-spacing: 0.3px;
}

/* Desktop Grid Layout */
.desktop-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, 1fr);
  grid-gap: 24px;
  gap: 24px;
  max-width: 1000px;
  margin: 0 auto;
}

/* Mobile Categories Container */
.mobile-categories-container {
  display: none;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.mobile-categories-slider {
  display: flex;
  transition: transform 0.3s ease;
  width: 200%;
  height: auto;
  will-change: transform;
}

.mobile-slide {
  width: 50%;
  flex-shrink: 0;
  padding: 0 15px;
  box-sizing: border-box;
  min-height: 300px;
}

.mobile-categories-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  grid-gap: 20px;
  gap: 20px;
  width: 100%;
  height: 100%;
}

/* Slide Indicators */
.slide-indicators {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 30px;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background-color: #b7b7b7;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.indicator.active {
  background-color: #000000;
}

.indicator:hover {
  background-color: #b8941f;
}

/* Category Items */
.category-item {
  position: relative;
  transition: all 0.3s ease;
}

.category-item:hover {
  transform: translateY(-2px);
}

.category-link {
  display: block;
  text-decoration: none;
  color: inherit;
  height: 100%;
}

.category-card {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  height: 200px;
  display: flex;
  flex-direction: column;
  position: relative;
  margin-bottom: 12px;
}

.category-item:hover .category-card {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.category-image-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.category-item:hover .category-image {
  transform: scale(1.02);
}

.category-title {
  font-size: 0.85rem;
  font-weight: 600;
  color: #2c2c2c;
  margin: 0;
  padding: 8px 0;
  text-align: center;
  letter-spacing: 1px;
  text-transform: uppercase;
  background: transparent;
  display: block;
}

/* View All Section */
.category-view-all {
  color: #333333;
}

.view-all-link {
  color: inherit;
  height: 100%;
}

.view-all-card {
  background: #f8f8f8;
  border-radius: 16px;
  border: 1px solid rgb(66, 0, 0);
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.category-view-all:hover .view-all-card {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.view-all-content {
  margin-bottom: 24px;
}

.view-all-number {
  font-size: 3.5rem;
  font-weight: 400;
  margin: 0 0 8px 0;
  color: #2c2c2c;
  font-family: 'Georgia', serif;
  line-height: 1;
}

.view-all-text {
  font-size: 0.9rem;
  margin: 0;
  color: #666666;
  line-height: 1.3;
  font-weight: 400;
}

.view-all-title {
  font-size: 0.85rem;
  font-weight: 600;
  margin: 0;
  letter-spacing: 1px;
  text-transform: uppercase;
  color: #2c2c2c;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .desktop-grid {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 20px;
  }

  .categories-title {
    font-size: 2.4rem;
  }

  .category-image {
    height: 200px;
  }

  .categories-section {
    padding: 60px 30px;
  }
}

@media (max-width: 768px) {
  .categories-section {
    padding: 50px 20px;
  }

  /* Hide desktop grid, show mobile container */
  .desktop-grid {
    display: none;
  }

  .mobile-categories-container {
    display: block;
  }

  .categories-title {
    font-size: 2.2rem;
  }

  .categories-subtitle {
    font-size: 1rem;
  }

  .category-image {
    height: 180px;
  }

  .category-title {
    font-size: 0.75rem;
    padding: 14px 16px;
    letter-spacing: 0.8px;
  }

  .view-all-number {
    font-size: 3rem;
  }

  .view-all-text {
    font-size: 0.8rem;
  }

  .view-all-title {
    font-size: 0.75rem;
  }

  .view-all-card {
    padding: 30px 20px;
    border: 1px solid maroon;
  }
}

@media (max-width: 480px) {
  .categories-section {
    padding: 40px 16px;
  }

  .categories-title {
    font-size: 2rem;
  }

  .categories-subtitle {
    font-size: 0.9rem;
  }

  .mobile-categories-grid {
    gap: 16px;
  }

  .category-image {
    height: 160px;
  }

  .category-title {
    font-size: 0.7rem;
    padding: 12px 14px;
    letter-spacing: 0.6px;
  }

  .view-all-number {
    font-size: 2.8rem;
  }

  .view-all-text {
    font-size: 0.75rem;
  }

  .view-all-title {
    font-size: 0.7rem;
  }

  .view-all-card {
    padding: 25px 16px;
    border: 1px solid maroon;
  }

  /* Border for View All in mobile */
  .category-view-all .view-all-content {
    border: 2px solid #8B4513;
    border-radius: 12px;
    padding: 20px;
  }

  .category-view-all .view-all-card {
    border: 2px solid #8B4513;
    border-radius: 12px;
  }


}

/* Category-specific background colors to match Tanishq design */
.category-earrings .category-card {
  background: linear-gradient(135deg, #f5f1e8 0%, #e8dcc0 100%);
}

.category-rings .category-card {
  background: linear-gradient(135deg, #f4e6d7 0%, #e6d4b7 100%);
}

.category-pendants .category-card {
  background: linear-gradient(135deg, #e8e3f0 0%, #d4c5e8 100%);
}

.category-mangalsutra .category-card {
  background: linear-gradient(135deg, #d8e8e0 0%, #b8d4c0 100%);
}

.category-bracelets .category-card {
  background: linear-gradient(135deg, #e0f0f8 0%, #c0e0f0 100%);
}

.category-bangles .category-card {
  background: linear-gradient(135deg, #f5f1e8 0%, #e8dcc0 100%);
}

.category-chains .category-card {
  background: linear-gradient(135deg, #f4e6d7 0%, #e6d4b7 100%);
}



/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/style/collection.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/* Collection Component Styles */

/* Ensure proper responsive behavior */
.desktop-layout {
  display: none;
}

.mobile-layout {
  display: block;
}

@media (min-width: 768px) {
  .desktop-layout {
    display: block !important;
  }

  .mobile-layout {
    display: none !important;
  }
}

.collections {
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px 20px;
  background-color: #ffffff;
}

#header {
  text-align: center;
  font-size: 3rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 2px;
  background: linear-gradient(45deg, #d4af37, #ffd700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

#subtitle {
  text-align: center;
  font-size: 1.2rem;
  color: #7f8c8d;
  margin-bottom: 50px;
  font-weight: 300;
  letter-spacing: 1px;
}

.parent {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(5, 1fr);
  grid-gap: 15px;
  gap: 15px;
  height: 700px;
  margin-bottom: 40px;
}

.div1, .div2, .div3, .div4, .div5 {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.div1:hover, .div2:hover, .div3:hover, .div4:hover, .div5:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.div1 {
  grid-column: span 2 / span 2;
  grid-row: span 5 / span 5;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
}

.div2 {
  grid-column: span 2 / span 2;
  grid-row: span 2 / span 2;
  grid-column-start: 3;
  background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
}

.div3 {
  grid-column: span 2 / span 2;
  grid-row: span 3 / span 3;
  grid-column-start: 5;
  background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
}

.div4 {
  grid-column: span 2 / span 2;
  grid-row: span 3 / span 3;
  grid-column-start: 3;
  grid-row-start: 3;
  background: linear-gradient(45deg, #43e97b 0%, #38f9d7 100%);
}

.div5 {
  grid-column: span 2 / span 2;
  grid-row: span 2 / span 2;
  grid-column-start: 5;
  grid-row-start: 4;
  background: linear-gradient(45deg, #fa709a 0%, #fee140 100%);
}

.div1 img, .div2 img, .div3 img, .div4 img, .div5 img {
  width: 100%;
  height: 100%;
  object-fit: fill;
  object-position: center;
  transition: transform 0.3s ease;
}

.div1 a, .div2 a, .div3 a, .div4 a, .div5 a {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
}

/* Bootstrap utility class for border radius */
.br-12 {
  border-radius: 12px !important;
}

.div1:hover img, .div2:hover img, .div3:hover img, .div4:hover img, .div5:hover img {
  transform: scale(1.05);
}

/* Overlay effect */
.div1::before, .div2::before, .div3::before, .div4::before, .div5::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.div1:hover::before, .div2:hover::before, .div3:hover::before, .div4:hover::before, .div5:hover::before {
  opacity: 1;
}

/* Responsive Design with Bootstrap */
@media (max-width: 1200px) {
  .parent {
    height: 600px;
    gap: 12px;
  }

  #header {
    font-size: 2.5rem;
  }
}

/* Mobile specific styles */
@media (max-width: 767.98px) {
  #header {
    font-size: 2rem;
  }

  #subtitle {
    font-size: 1rem;
    margin-bottom: 30px;
  }

  .collections {
    padding: 20px 10px;
  }

  /* Mobile image containers */
  .mobile-layout .position-relative {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    margin-bottom: 16px;
    height: 280px;
    background: #f8f9fa;
  }

  .mobile-layout .position-relative:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
  }

  .mobile-layout .position-relative img {
    width: 100%;
    height: 100%;
    object-fit: fill;
    object-position: center;
    transition: transform 0.3s ease;
  }

  .mobile-layout .position-relative:hover img {
    transform: scale(1.02);
  }

  /* First image - larger featured image */
  .mobile-layout .position-relative:first-child {
    height: 320px;
    margin-bottom: 20px;
  }

  /* Remove margin from last item */
  .mobile-layout .position-relative:last-child {
    margin-bottom: 0;
  }

  /* Mobile layout spacing */
  .mobile-layout {
    padding: 0 16px;
  }

  /* Mobile collection link */
  .mobile-collection-link {
    display: block;
    text-decoration: none;
    color: inherit;
    position: relative;
    width: 100%;
    height: 100%;
  }

  /* Mobile text overlay */
  .mobile-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    padding: 40px 20px 20px;
    color: white;
    z-index: 2;
  }

  .mobile-text-content {
    text-align: left;
  }

  .mobile-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    line-height: 1.2;
  }

  .mobile-subtitle {
    font-size: 0.9rem;
    font-weight: 300;
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    line-height: 1.3;
  }

  /* Hover effect for mobile */
  .mobile-collection-link:hover .mobile-overlay {
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  }

  .mobile-collection-link:hover .mobile-title {
    color: #ffd700;
    transition: color 0.3s ease;
  }
}
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/style/Radhe-world.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/* Radhe World Component Styles - Matching Tanishq World Design */
.Radhe-world {
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 20px;
  font-family: 'Georgia', serif;
  background-color: #fafafa;
}

/* Header Styles */
.Radhe-header {
  text-align: center;
  margin-bottom: 50px;
}

.Radhe-title {
  font-size: 2.8rem;
  font-weight: 400;
  color: #8B4513;
  margin: 0 0 15px 0;
  letter-spacing: 1px;
  font-family: 'Georgia', serif;
}

.Radhe-subtitle {
  font-size: 1.2rem;
  color: #888;
  margin: 0;
  font-style: italic;
  font-weight: 300;
  letter-spacing: 0.5px;
}

/* Grid Layout - 2x2 Grid */
.Radhe-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  grid-gap: 20px;
  gap: 20px;
  height: 650px;
  max-width: 900px;
  margin: 0 auto;
}

/* Individual Item Styles */
.Radhe-item {
  position: relative;
  overflow: hidden;
  border-radius: 15px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.Radhe-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.Radhe-link {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  color: inherit;
}

.Radhe-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.Radhe-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.Radhe-item:hover .Radhe-image {
  transform: scale(1.08);
}

/* Overlay Styles */
.Radhe-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.75));
  padding: 40px 25px 25px;
  display: flex;
  align-items: flex-end;
}

.Radhe-item-title {
  color: white;
  font-size: 2rem;
  font-weight: 500;
  margin: 0;
  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.7);
  letter-spacing: 1px;
  font-family: 'Georgia', serif;
}

/* Specific Grid Positioning */
.wedding-item {
  grid-column: 1;
  grid-row: 1;
}

.diamond-item {
  grid-column: 2;
  grid-row: 1;
}

.gold-item {
  grid-column: 1;
  grid-row: 2;
}

.dailywear-item {
  grid-column: 2;
  grid-row: 2;
}

/* Responsive Design */
@media (max-width: 768px) {
  .Radhe-world {
    padding: 40px 15px;
  }

  .Radhe-title {
    font-size: 2.2rem;
  }

  .Radhe-subtitle {
    font-size: 1rem;
  }

  .Radhe-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 280px);
    height: auto;
    gap: 15px;
    max-width: 100%;
  }

  .Radhe-item-title {
    font-size: 1.6rem;
  }

  .Radhe-overlay {
    padding: 25px 20px 20px;
  }

  /* Reset grid positioning for mobile */
  .wedding-item,
  .diamond-item,
  .gold-item,
  .dailywear-item {
    grid-column: 1;
    grid-row: auto;
  }
}

@media (max-width: 480px) {
  .Radhe-world {
    padding: 30px 10px;
  }

  .Radhe-title {
    font-size: 1.9rem;
  }

  .Radhe-subtitle {
    font-size: 0.9rem;
  }

  .Radhe-grid {
    grid-template-rows: repeat(4, 250px);
    gap: 12px;
  }

  .Radhe-item-title {
    font-size: 1.4rem;
  }

  .Radhe-overlay {
    padding: 20px 15px 15px;
  }
}

/* Large screens optimization */
@media (min-width: 1200px) {
  .Radhe-world {
    padding: 80px 20px;
  }

  .Radhe-title {
    font-size: 3.2rem;
  }

  .Radhe-subtitle {
    font-size: 1.3rem;
  }

  .Radhe-grid {
    height: 700px;
    max-width: 1000px;
  }

  .Radhe-item-title {
    font-size: 2.2rem;
  }
}

/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/style/trending.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/* Trending Component Styles */

.trending-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 80px 40px;
  background-color: #ffffff;
}

/* Header Styles */
.trending-header {
  text-align: center;
  margin-bottom: 60px;
}

.trending-title {
  font-size: 2.8rem;
  font-weight: 400;
  color: #2c2c2c;
  margin-bottom: 12px;
  font-family: 'Georgia', serif;
  line-height: 1.2;
}

.trending-subtitle {
  font-size: 1.1rem;
  color: #8a8a8a;
  margin-bottom: 0;
  font-weight: 400;
  letter-spacing: 0.3px;
}

/* Grid Layout */
.trending-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 30px;
  gap: 30px;
  max-width: 1000px;
  margin: 0 auto;
}

/* Trending Item Styles */
.trending-item {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background: #ffffff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.trending-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.trending-link {
  display: block;
  text-decoration: none;
  color: inherit;
  width: 100%;
  height: 100%;
}

.trending-card {
  position: relative;
  width: 100%;
  height: 100%;
}

.trending-image-container {
  position: relative;
  width: 100%;
  height: 350px;
  overflow: hidden;
  border-radius: 12px;
}

.trending-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.trending-item:hover .trending-image {
  transform: scale(1.05);
}

/* Overlay Styles */
.trending-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    transparent 100%
  );
  padding: 30px 20px 20px;
  transition: opacity 0.3s ease;
}

.trending-overlay-content {
  text-align: center;
}

.trending-overlay-title {
  color: #ffffff;
  font-size: 1.4rem;
  font-weight: 500;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .trending-section {
    padding: 60px 30px;
  }
  
  .trending-grid {
    gap: 25px;
  }
  
  .trending-image-container {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .trending-section {
    padding: 50px 20px;
  }
  
  .trending-title {
    font-size: 2.2rem;
  }
  
  .trending-subtitle {
    font-size: 1rem;
  }
  
  .trending-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    max-width: 400px;
  }
  
  .trending-image-container {
    height: 280px;
  }
  
  .trending-overlay-title {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .trending-section {
    padding: 40px 15px;
  }
  
  .trending-title {
    font-size: 1.8rem;
  }
  
  .trending-subtitle {
    font-size: 0.9rem;
  }
  
  .trending-image-container {
    height: 250px;
  }
  
  .trending-overlay {
    padding: 20px 15px 15px;
  }
  
  .trending-overlay-title {
    font-size: 1.1rem;
  }
}

/* Specific styling for different trending categories */
.trending-auspicious .trending-overlay {
  background: linear-gradient(
    to top,
    rgba(139, 69, 19, 0.8) 0%,
    rgba(139, 69, 19, 0.4) 50%,
    transparent 100%
  );
}

.trending-gifting .trending-overlay {
  background: linear-gradient(
    to top,
    rgba(220, 20, 60, 0.8) 0%,
    rgba(220, 20, 60, 0.4) 50%,
    transparent 100%
  );
}

.trending-18kt .trending-overlay {
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    transparent 100%
  );
}

/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/style/new-arrivals.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
.new-arrivals {
  background: linear-gradient(135deg, #d4b896 0%, #c9a876 50%, #b8956a 100%);
  position: relative;
  padding: 80px 0;
  overflow: hidden;
}

.new-arrivals::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 40%;
  height: 100%;
  background: url('/main-images/image-3.png') no-repeat right center;
  background-size: contain;
  opacity: 0.2;
  z-index: 1;
}

.new-arrivals-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

.new-arrivals-content {
  display: flex;
  align-items: center;
  gap: 60px;
}

.new-arrivals-text {
  flex: 1 1;
  color: white;
}

.new-arrivals-title {
  font-size: 3rem;
  font-weight: 300;
  margin: 0 0 20px 0;
  letter-spacing: 2px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.new-arrivals-subtitle {
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
  opacity: 0.95;
  font-weight: 300;
}

.new-arrivals-products {
  display: flex;
  gap: 20px;
  flex: 1 1;
}

.product-card {
  flex: 1 1;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
}

.product-image {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.product-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-img {
  transform: scale(1.05);
}

.product-label {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 30px 20px 20px;
  color: white;
}

.product-label span {
  font-size: 1.2rem;
  font-weight: 500;
  letter-spacing: 1px;
  text-transform: uppercase;
}

/* Responsive Design */
@media (max-width: 768px) {
  .new-arrivals {
    padding: 60px 0;
  }
  
  .new-arrivals-content {
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }
  
  .new-arrivals-title {
    font-size: 2.5rem;
  }
  
  .new-arrivals-subtitle {
    font-size: 1rem;
  }
  
  .new-arrivals-products {
    flex-direction: column;
    gap: 20px;
  }
  
  .product-image {
    height: 200px;
  }
}

@media (max-width: 480px) {
  .new-arrivals {
    padding: 40px 0;
  }
  
  .new-arrivals-container {
    padding: 0 15px;
  }
  
  .new-arrivals-title {
    font-size: 2rem;
  }
  
  .new-arrivals-subtitle {
    font-size: 0.9rem;
  }
  
  .product-image {
    height: 180px;
  }
  
  .product-label span {
    font-size: 1rem;
  }
}

