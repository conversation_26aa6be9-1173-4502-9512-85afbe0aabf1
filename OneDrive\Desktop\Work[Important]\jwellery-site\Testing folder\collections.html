<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Collection</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>

<body>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .collections {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        #header {
            text-align: center;
            font-size: 3rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 2px;
            background: linear-gradient(45deg, #d4af37, #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        #subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: #7f8c8d;
            margin-bottom: 50px;
            font-weight: 300;
            letter-spacing: 1px;
        }

        .parent {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            grid-template-rows: repeat(5, 1fr);
            gap: 15px;
            height: 700px;
            margin-bottom: 40px;
        }

        .div1, .div2, .div3, .div4, .div5 {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
        }

        .div1:hover, .div2:hover, .div3:hover, .div4:hover, .div5:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .div1 {
            grid-column: span 2 / span 2;
            grid-row: span 5 / span 5;
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
        }

        .div2 {
            grid-column: span 2 / span 2;
            grid-row: span 2 / span 2;
            grid-column-start: 3;
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
        }

        .div3 {
            grid-column: span 2 / span 2;
            grid-row: span 3 / span 3;
            grid-column-start: 5;
            background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
        }

        .div4 {
            grid-column: span 2 / span 2;
            grid-row: span 3 / span 3;
            grid-column-start: 3;
            grid-row-start: 3;
            background: linear-gradient(45deg, #43e97b 0%, #38f9d7 100%);
        }

        .div5 {
            grid-column: span 2 / span 2;
            grid-row: span 2 / span 2;
            grid-column-start: 5;
            grid-row-start: 4;
            background: linear-gradient(45deg, #fa709a 0%, #fee140 100%);
        }

        .div1 img, .div2 img, .div3 img, .div4 img, .div5 img {
            width: 100%;
            height: 100%;
            object-fit: fill;
            object-position: center;
            transition: transform 0.3s ease;
        }

        .div1 a, .div2 a, .div3 a, .div4 a, .div5 a {
            display: block;
            width: 100%;
            height: 100%;
            text-decoration: none;
        }

        /* Bootstrap utility class for border radius */
        .br-12 {
            border-radius: 12px !important;
        }

        .div1:hover img, .div2:hover img, .div3:hover img, .div4:hover img, .div5:hover img {
            transform: scale(1.05);
        }

        /* Overlay effect */
        .div1::before, .div2::before, .div3::before, .div4::before, .div5::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .div1:hover::before, .div2:hover::before, .div3:hover::before, .div4:hover::before, .div5:hover::before {
            opacity: 1;
        }

        /* Responsive Design with Bootstrap */
        @media (max-width: 1200px) {
            .parent {
                height: 600px;
                gap: 12px;
            }

            #header {
                font-size: 2.5rem;
            }
        }

        /* Mobile specific styles */
        @media (max-width: 767.98px) {
            #header {
                font-size: 2rem;
            }

            #subtitle {
                font-size: 1rem;
                margin-bottom: 30px;
            }

            .collections {
                padding: 20px 10px;
            }

            /* Mobile image containers */
            .d-md-none .position-relative {
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
                transition: all 0.3s ease;
                background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            }

            .d-md-none .position-relative:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            }

            .d-md-none .position-relative:nth-child(2) {
                background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            }

            .d-md-none .position-relative:nth-child(3) {
                background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
            }

            .d-md-none .position-relative:nth-child(4) {
                background: linear-gradient(45deg, #43e97b 0%, #38f9d7 100%);
            }

            .d-md-none .position-relative:nth-child(5) {
                background: linear-gradient(45deg, #fa709a 0%, #fee140 100%);
            }
        }
    </style>
    <div class="collections">
        <h1 id="header">Radhe-Radhe collections</h1>
        <h2 id="subtitle">Shop the latest collections from our exclusive brands</h2>

        <!-- Desktop and Tablet Grid Layout -->
        <div class="d-none d-md-block">
            <div class="parent">
                <div class="div1">
                    <a href="#collection1">
                        <img src="../public/main-images/image-1.png" alt="Image 1" id="image-1" class="img-fluid">
                    </a>
                </div>
                <div class="div2">
                    <a href="#collection2">
                        <img src="../public/main-images/image-2.png" alt="Image 2" id="image-2" class="img-fluid">
                    </a>
                </div>
                <div class="div3">
                    <a href="#collection3">
                        <img src="../public/main-images/image-3.png" alt="Image 3" id="image-3" class="img-fluid">
                    </a>
                </div>
                <div class="div4">
                    <a href="#collection4">
                        <img src="../public/main-images/image-4.png" alt="Image 4" id="image-4" class="img-fluid">
                    </a>
                </div>
                <div class="div5">
                    <a href="#collection5">
                        <img src="../public/main-images/image-5.png" alt="Image 5" id="image-5" class="img-fluid">
                    </a>
                </div>
            </div>
        </div>

        <!-- Mobile Layout -->
        <div class="d-md-none d-flex flex-column">
            <div class="position-relative w-100 mb-3">
                <a href="#collection1">
                    <img loading="lazy" alt="Collection 1" class="img-fluid w-100 br-12" src="../public/main-images/image-1.png">
                </a>
            </div>
            <div class="position-relative w-100 mb-3">
                <a href="#collection2">
                    <img loading="lazy" alt="Collection 2" class="img-fluid w-100 br-12" src="../public/main-images/image-2.png">
                </a>
            </div>
            <div class="position-relative w-100 mb-3">
                <a href="#collection3">
                    <img loading="lazy" alt="Collection 3" class="img-fluid w-100 br-12" src="../public/main-images/image-3.png">
                </a>
            </div>
            <div class="position-relative w-100 mb-3">
                <a href="#collection4">
                    <img loading="lazy" alt="Collection 4" class="img-fluid w-100 br-12" src="../public/main-images/image-4.png">
                </a>
            </div>
            <div class="position-relative w-100 mb-3">
                <a href="#collection5">
                    <img loading="lazy" alt="Collection 5" class="img-fluid w-100 br-12" src="../public/main-images/image-5.png">
                </a>
            </div>
        </div>

    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>